import pygame
import math
import numpy as np

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60
BACKGROUND_COLOR = (5, 5, 15)  # Dark blue space
SUN_COLOR = (255, 255, 0)     # Yellow
TRAIL_LENGTH = 150
STAR_COUNT = 200

# Gravitational constant (scaled for simulation)
G = 6.67430e-11 * 1e9  # Scaled up for visible effects

class CelestialBody:
    """Base class for all celestial objects"""
    def __init__(self, x, y, radius, mass, color, name=""):
        self.x = x
        self.y = y
        self.radius = radius
        self.mass = mass
        self.color = color
        self.name = name
        self.vx = 0  # velocity in x direction
        self.vy = 0  # velocity in y direction
        self.trail = []  # Store previous positions for trail effect
        
    def update_position(self, dt):
        """Update position based on velocity"""
        self.x += self.vx * dt
        self.y += self.vy * dt
        
        # Add current position to trail
        self.trail.append((int(self.x), int(self.y)))
        if len(self.trail) > TRAIL_LENGTH:
            self.trail.pop(0)
    
    def apply_force(self, fx, fy, dt):
        """Apply force to update velocity"""
        ax = fx / self.mass
        ay = fy / self.mass
        self.vx += ax * dt
        self.vy += ay * dt
    
    def draw(self, screen, camera_x=0, camera_y=0, zoom=1.0, show_trails=True, show_labels=True):
        """Draw the celestial body on screen"""
        screen_x = int((self.x - camera_x) * zoom + SCREEN_WIDTH // 2)
        screen_y = int((self.y - camera_y) * zoom + SCREEN_HEIGHT // 2)
        screen_radius = max(2, int(self.radius * zoom))

        # Draw trail
        if show_trails and len(self.trail) > 1:
            trail_points = []
            for i, (tx, ty) in enumerate(self.trail):
                trail_x = int((tx - camera_x) * zoom + SCREEN_WIDTH // 2)
                trail_y = int((ty - camera_y) * zoom + SCREEN_HEIGHT // 2)
                if -50 <= trail_x <= SCREEN_WIDTH + 50 and -50 <= trail_y <= SCREEN_HEIGHT + 50:
                    trail_points.append((trail_x, trail_y))

            if len(trail_points) > 1:
                # Draw trail with fading effect
                for i in range(len(trail_points) - 1):
                    alpha = max(50, int(255 * (i + 1) / len(trail_points)))
                    # Create a faded version of the planet color
                    faded_color = tuple(int(c * alpha / 255) for c in self.color)
                    if i < len(trail_points) - 1:
                        pygame.draw.line(screen, faded_color, trail_points[i], trail_points[i + 1], max(1, int(2 * zoom)))

        # Draw the body
        if -50 <= screen_x <= SCREEN_WIDTH + 50 and -50 <= screen_y <= SCREEN_HEIGHT + 50:
            pygame.draw.circle(screen, self.color, (screen_x, screen_y), screen_radius)

            # Add a subtle glow for planets
            if screen_radius > 3:
                glow_color = tuple(min(255, c + 30) for c in self.color)
                pygame.draw.circle(screen, glow_color, (screen_x, screen_y), screen_radius + 1, 1)

            # Draw name label
            if show_labels and zoom > 0.3 and self.name:
                font_size = max(16, min(24, int(20 * zoom)))
                font = pygame.font.Font(None, font_size)
                text = font.render(self.name, True, (255, 255, 255))
                screen.blit(text, (screen_x + screen_radius + 5, screen_y - 10))

class Sun(CelestialBody):
    """Sun class - stationary at center"""
    def __init__(self):
        super().__init__(0, 0, 20, 1.989e30, SUN_COLOR, "Sun")
        
    def draw(self, screen, camera_x=0, camera_y=0, zoom=1.0, show_trails=True, show_labels=True):
        """Draw the sun with a glow effect"""
        screen_x = int((self.x - camera_x) * zoom + SCREEN_WIDTH // 2)
        screen_y = int((self.y - camera_y) * zoom + SCREEN_HEIGHT // 2)
        screen_radius = max(8, int(self.radius * zoom))

        # Draw glow effect
        for i in range(4):
            glow_radius = screen_radius + (4 - i) * 4
            glow_intensity = 80 - i * 15
            glow_color = (255, 255, min(255, 150 + glow_intensity))
            pygame.draw.circle(screen, glow_color, (screen_x, screen_y), glow_radius)

        # Draw the sun core
        pygame.draw.circle(screen, self.color, (screen_x, screen_y), screen_radius)

        # Draw bright center
        center_radius = max(2, screen_radius // 2)
        pygame.draw.circle(screen, (255, 255, 255), (screen_x, screen_y), center_radius)

        # Draw name
        if show_labels and zoom > 0.2:
            font_size = max(20, min(32, int(28 * zoom)))
            font = pygame.font.Font(None, font_size)
            text = font.render(self.name, True, (255, 255, 255))
            screen.blit(text, (screen_x + screen_radius + 8, screen_y - 12))

class Planet(CelestialBody):
    """Planet class with orbital mechanics"""
    def __init__(self, name, distance, radius, mass, color, orbital_speed):
        # Start planet at the specified distance from sun
        super().__init__(distance, 0, radius, mass, color, name)
        
        # Set initial orbital velocity (perpendicular to position)
        self.vy = orbital_speed
        self.distance = distance
        self.orbital_speed = orbital_speed
    
    def calculate_gravitational_force(self, sun):
        """Calculate gravitational force from the sun"""
        dx = sun.x - self.x
        dy = sun.y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance == 0:
            return 0, 0
        
        # F = G * m1 * m2 / r^2
        force_magnitude = G * self.mass * sun.mass / (distance**2)
        
        # Force direction (unit vector)
        fx = force_magnitude * dx / distance
        fy = force_magnitude * dy / distance
        
        return fx, fy

class SolarSystem:
    """Main solar system simulation class"""
    def __init__(self):
        self.sun = Sun()
        self.planets = []
        self.camera_x = 0
        self.camera_y = 0
        self.zoom = 1.0
        self.time_scale = 1.0
        self.paused = False
        self.show_trails = True
        self.show_labels = True

        # Create background stars
        self.stars = []
        self.create_stars()

        # Create planets with realistic-ish data (scaled for visualization)
        self.create_planets()

    def create_stars(self):
        """Create background stars for visual appeal"""
        import random
        for _ in range(STAR_COUNT):
            x = random.randint(-2000, 2000)
            y = random.randint(-2000, 2000)
            brightness = random.randint(50, 255)
            size = random.randint(1, 2)
            self.stars.append((x, y, brightness, size))

    def create_planets(self):
        """Create all planets with scaled orbital parameters"""
        # Planet data: (name, distance_from_sun, radius, mass, color, orbital_speed)
        planet_data = [
            ("Mercury", 80, 3, 3.301e23, (169, 169, 169), 0.8),
            ("Venus", 120, 4, 4.867e24, (255, 198, 73), 0.6),
            ("Earth", 160, 5, 5.972e24, (100, 149, 237), 0.5),
            ("Mars", 200, 4, 6.39e23, (205, 92, 92), 0.4),
            ("Jupiter", 300, 15, 1.898e27, (255, 165, 0), 0.25),
            ("Saturn", 400, 12, 5.683e26, (255, 215, 0), 0.2),
            ("Uranus", 500, 8, 8.681e25, (64, 224, 208), 0.15),
            ("Neptune", 600, 8, 1.024e26, (65, 105, 225), 0.12)
        ]

        for name, distance, radius, mass, color, speed in planet_data:
            planet = Planet(name, distance, radius, mass, color, speed)
            self.planets.append(planet)

    def update(self, dt):
        """Update all celestial bodies"""
        if self.paused:
            return

        scaled_dt = dt * self.time_scale

        # Update each planet
        for planet in self.planets:
            # Calculate gravitational force from sun
            fx, fy = planet.calculate_gravitational_force(self.sun)

            # Apply force to update velocity
            planet.apply_force(fx, fy, scaled_dt)

            # Update position
            planet.update_position(scaled_dt)

    def draw(self, screen):
        """Draw the entire solar system"""
        screen.fill(BACKGROUND_COLOR)

        # Draw background stars
        for star_x, star_y, brightness, size in self.stars:
            screen_x = int((star_x - self.camera_x) * self.zoom + SCREEN_WIDTH // 2)
            screen_y = int((star_y - self.camera_y) * self.zoom + SCREEN_HEIGHT // 2)
            if -50 <= screen_x <= SCREEN_WIDTH + 50 and -50 <= screen_y <= SCREEN_HEIGHT + 50:
                color = (brightness, brightness, brightness)
                pygame.draw.circle(screen, color, (screen_x, screen_y), size)

        # Draw orbital paths (faint circles)
        if self.zoom > 0.3:
            for planet in self.planets:
                center_x = int((0 - self.camera_x) * self.zoom + SCREEN_WIDTH // 2)
                center_y = int((0 - self.camera_y) * self.zoom + SCREEN_HEIGHT // 2)
                radius = int(planet.distance * self.zoom)
                if radius > 10 and radius < 2000:  # Only draw if reasonable size
                    pygame.draw.circle(screen, (30, 30, 30), (center_x, center_y), radius, 1)

        # Draw sun
        self.sun.draw(screen, self.camera_x, self.camera_y, self.zoom, self.show_trails, self.show_labels)

        # Draw planets
        for planet in self.planets:
            planet.draw(screen, self.camera_x, self.camera_y, self.zoom, self.show_trails, self.show_labels)

    def handle_input(self, keys, mouse_wheel=0):
        """Handle user input for camera controls"""
        camera_speed = 5 / self.zoom

        # Camera movement
        if keys[pygame.K_LEFT] or keys[pygame.K_a]:
            self.camera_x -= camera_speed
        if keys[pygame.K_RIGHT] or keys[pygame.K_d]:
            self.camera_x += camera_speed
        if keys[pygame.K_UP] or keys[pygame.K_w]:
            self.camera_y -= camera_speed
        if keys[pygame.K_DOWN] or keys[pygame.K_s]:
            self.camera_y += camera_speed

        # Zoom
        if mouse_wheel > 0:
            self.zoom = min(self.zoom * 1.1, 5.0)
        elif mouse_wheel < 0:
            self.zoom = max(self.zoom * 0.9, 0.1)

        # Time controls
        if keys[pygame.K_PLUS] or keys[pygame.K_EQUALS]:
            self.time_scale = min(self.time_scale * 1.1, 10.0)
        if keys[pygame.K_MINUS]:
            self.time_scale = max(self.time_scale * 0.9, 0.1)

        # Reset camera
        if keys[pygame.K_r]:
            self.camera_x = 0
            self.camera_y = 0
            self.zoom = 1.0

        # Toggle trails
        if keys[pygame.K_t]:
            self.show_trails = not self.show_trails

        # Toggle labels
        if keys[pygame.K_l]:
            self.show_labels = not self.show_labels

    def toggle_pause(self):
        """Toggle simulation pause"""
        self.paused = not self.paused

def draw_ui(screen, solar_system):
    """Draw user interface elements"""
    font = pygame.font.Font(None, 24)
    small_font = pygame.font.Font(None, 20)

    # Status text
    status_text = "PAUSED" if solar_system.paused else "RUNNING"
    status_color = (255, 100, 100) if solar_system.paused else (100, 255, 100)
    text = font.render(f"Status: {status_text}", True, status_color)
    screen.blit(text, (10, 10))

    # Time scale
    time_text = small_font.render(f"Time Scale: {solar_system.time_scale:.1f}x", True, (255, 255, 255))
    screen.blit(time_text, (10, 35))

    # Zoom level
    zoom_text = small_font.render(f"Zoom: {solar_system.zoom:.1f}x", True, (255, 255, 255))
    screen.blit(zoom_text, (10, 55))

    # Controls
    controls = [
        "Controls:",
        "WASD/Arrow Keys - Move camera",
        "Mouse Wheel - Zoom in/out",
        "+/- - Speed up/slow down time",
        "SPACE - Pause/Resume",
        "R - Reset camera",
        "T - Toggle trails",
        "L - Toggle labels",
        "ESC - Exit"
    ]

    for i, control in enumerate(controls):
        color = (255, 255, 100) if i == 0 else (200, 200, 200)
        text = small_font.render(control, True, color)
        screen.blit(text, (10, SCREEN_HEIGHT - 140 + i * 20))

def main():
    """Main game loop"""
    # Initialize Pygame
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Solar System Simulation")
    clock = pygame.time.Clock()

    # Create solar system
    solar_system = SolarSystem()

    # Main game loop
    running = True
    while running:
        dt = clock.tick(FPS) / 1000.0  # Delta time in seconds

        # Handle events
        mouse_wheel = 0
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    solar_system.toggle_pause()
            elif event.type == pygame.MOUSEWHEEL:
                mouse_wheel = event.y

        # Handle continuous input
        keys = pygame.key.get_pressed()
        solar_system.handle_input(keys, mouse_wheel)

        # Update simulation
        solar_system.update(dt)

        # Draw everything
        solar_system.draw(screen)
        draw_ui(screen, solar_system)

        # Update display
        pygame.display.flip()

    # Quit
    pygame.quit()

if __name__ == "__main__":
    main()
